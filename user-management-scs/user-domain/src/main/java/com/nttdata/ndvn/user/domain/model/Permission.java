package com.nttdata.ndvn.user.domain.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

import java.time.LocalDateTime;
import java.util.*;

/**
 * Permission entity representing a specific authorization to perform an action on a resource.
 * 
 * Permissions define fine-grained access control capabilities that can be grouped into roles
 * and assigned to users within the User Management bounded context.
 */
@Entity
@Table(name = "permissions", indexes = {
    @Index(name = "idx_permissions_name", columnList = "name"),
    @Index(name = "idx_permissions_resource", columnList = "resource"),
    @Index(name = "idx_permissions_action", columnList = "action")
})
public class Permission {
    
    @Id
    @Column(name = "id")
    private UUID id;
    
    @NotBlank(message = "Permission name is required")
    @Size(min = 2, max = 100, message = "Permission name must be between 2 and 100 characters")
    @Column(name = "name", unique = true, nullable = false, length = 100)
    private String name;
    
    @Size(max = 255, message = "Description must not exceed 255 characters")
    @Column(name = "description", length = 255)
    private String description;
    
    @NotBlank(message = "Resource is required")
    @Size(min = 2, max = 100, message = "Resource must be between 2 and 100 characters")
    @Column(name = "resource", nullable = false, length = 100)
    private String resource;
    
    @NotBlank(message = "Action is required")
    @Size(min = 2, max = 50, message = "Action must be between 2 and 50 characters")
    @Column(name = "action", nullable = false, length = 50)
    private String action;
    
    @Column(name = "system_permission", nullable = false)
    private boolean systemPermission = false;
    
    @Column(name = "active", nullable = false)
    private boolean active = true;
    
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;
    
    @ManyToMany(mappedBy = "permissions", fetch = FetchType.LAZY)
    private Set<Role> roles = new HashSet<>();
    
    // Constructors
    protected Permission() {
        // JPA constructor
    }
    
    public Permission(String name, String resource, String action) {
        this.id = UUID.randomUUID();
        this.name = name;
        this.resource = resource;
        this.action = action;
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
    
    public Permission(String name, String description, String resource, String action) {
        this(name, resource, action);
        this.description = description;
    }
    
    // Business methods
    
    /**
     * Checks if this permission applies to the specified resource and action.
     * 
     * @param resource the resource to check
     * @param action the action to check
     * @return true if this permission applies, false otherwise
     */
    public boolean appliesTo(String resource, String action) {
        return this.resource.equals(resource) && this.action.equals(action);
    }
    
    /**
     * Checks if this permission applies to the specified resource with any action.
     * 
     * @param resource the resource to check
     * @return true if this permission applies to the resource, false otherwise
     */
    public boolean appliesToResource(String resource) {
        return this.resource.equals(resource);
    }
    
    /**
     * Activates this permission.
     */
    public void activate() {
        this.active = true;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * Deactivates this permission.
     */
    public void deactivate() {
        this.active = false;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * Marks this permission as a system permission.
     * System permissions cannot be deleted and have restricted modification.
     */
    public void markAsSystemPermission() {
        this.systemPermission = true;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * Checks if this permission can be deleted.
     * System permissions and permissions assigned to roles cannot be deleted.
     * 
     * @return true if the permission can be deleted, false otherwise
     */
    public boolean canBeDeleted() {
        return !systemPermission && roles.isEmpty();
    }
    
    /**
     * Updates the permission description.
     * 
     * @param description the new description
     */
    public void updateDescription(String description) {
        this.description = description;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * Creates a permission name from resource and action.
     * 
     * @param resource the resource
     * @param action the action
     * @return the generated permission name
     */
    public static String createPermissionName(String resource, String action) {
        return resource.toUpperCase() + "_" + action.toUpperCase();
    }
    
    // JPA lifecycle callbacks
    
    @PrePersist
    protected void onCreate() {
        if (id == null) {
            id = UUID.randomUUID();
        }
        LocalDateTime now = LocalDateTime.now();
        createdAt = now;
        updatedAt = now;
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    // Getters and setters
    
    public UUID getId() {
        return id;
    }
    
    public void setId(UUID id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
        this.updatedAt = LocalDateTime.now();
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
        this.updatedAt = LocalDateTime.now();
    }
    
    public String getResource() {
        return resource;
    }
    
    public void setResource(String resource) {
        this.resource = resource;
        this.updatedAt = LocalDateTime.now();
    }
    
    public String getAction() {
        return action;
    }
    
    public void setAction(String action) {
        this.action = action;
        this.updatedAt = LocalDateTime.now();
    }
    
    public boolean isSystemPermission() {
        return systemPermission;
    }
    
    public boolean isActive() {
        return active;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public Set<Role> getRoles() {
        return Collections.unmodifiableSet(roles);
    }
    
    // equals, hashCode, toString
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Permission that = (Permission) o;
        return Objects.equals(id, that.id);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(id);
    }
    
    @Override
    public String toString() {
        return "Permission{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", resource='" + resource + '\'' +
                ", action='" + action + '\'' +
                ", systemPermission=" + systemPermission +
                ", active=" + active +
                '}';
    }
}
