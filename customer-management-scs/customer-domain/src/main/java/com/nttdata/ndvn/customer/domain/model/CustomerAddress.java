package com.nttdata.ndvn.customer.domain.model;

import jakarta.persistence.*;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.UUID;

/**
 * Customer address entity representing a customer's address information.
 */
@Entity
@Table(name = "customer_addresses", indexes = {
    @Index(name = "idx_customer_address_customer_id", columnList = "customer_id"),
    @Index(name = "idx_customer_address_type", columnList = "addressType"),
    @Index(name = "idx_customer_address_primary", columnList = "isPrimary"),
    @Index(name = "idx_customer_address_active", columnList = "isActive")
})
public class CustomerAddress {
    
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "customer_id", nullable = false)
    private Customer customer;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "address_type", nullable = false)
    private AddressType addressType = AddressType.BILLING;
    
    @Column(name = "street_address", nullable = false)
    private String streetAddress;
    
    @Column(name = "address_line_2")
    private String addressLine2;
    
    @Column(name = "city", nullable = false, length = 100)
    private String city;
    
    @Column(name = "state", length = 50)
    private String state;
    
    @Column(name = "postal_code", nullable = false, length = 20)
    private String postalCode;
    
    @Column(name = "country", nullable = false, length = 50)
    private String country = "VN";
    
    @Column(name = "is_primary")
    private boolean isPrimary = false;
    
    @Column(name = "is_active")
    private boolean isActive = true;
    
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;
    
    // Constructors
    protected CustomerAddress() {
        // JPA constructor
    }
    
    private CustomerAddress(Builder builder) {
        this.addressType = builder.addressType;
        this.streetAddress = builder.streetAddress;
        this.addressLine2 = builder.addressLine2;
        this.city = builder.city;
        this.state = builder.state;
        this.postalCode = builder.postalCode;
        this.country = builder.country;
        this.isPrimary = builder.isPrimary;
        this.isActive = builder.isActive;
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
    
    public static Builder builder() {
        return new Builder();
    }
    
    // Business methods
    public void updateAddress(String streetAddress, String addressLine2, String city, 
                            String state, String postalCode, String country) {
        this.streetAddress = streetAddress;
        this.addressLine2 = addressLine2;
        this.city = city;
        this.state = state;
        this.postalCode = postalCode;
        this.country = country;
        this.updatedAt = LocalDateTime.now();
    }
    
    public void setPrimary(boolean primary) {
        this.isPrimary = primary;
        this.updatedAt = LocalDateTime.now();
    }
    
    public void setActive(boolean active) {
        this.isActive = active;
        this.updatedAt = LocalDateTime.now();
    }
    
    public String getFullAddress() {
        StringBuilder sb = new StringBuilder();
        sb.append(streetAddress);
        if (addressLine2 != null && !addressLine2.trim().isEmpty()) {
            sb.append(", ").append(addressLine2);
        }
        sb.append(", ").append(city);
        if (state != null && !state.trim().isEmpty()) {
            sb.append(", ").append(state);
        }
        sb.append(" ").append(postalCode);
        sb.append(", ").append(country);
        return sb.toString();
    }
    
    // Builder pattern
    public static class Builder {
        private AddressType addressType = AddressType.BILLING;
        private String streetAddress;
        private String addressLine2;
        private String city;
        private String state;
        private String postalCode;
        private String country = "VN";
        private boolean isPrimary = false;
        private boolean isActive = true;
        
        public Builder addressType(AddressType addressType) {
            this.addressType = addressType;
            return this;
        }
        
        public Builder streetAddress(String streetAddress) {
            this.streetAddress = streetAddress;
            return this;
        }
        
        public Builder addressLine2(String addressLine2) {
            this.addressLine2 = addressLine2;
            return this;
        }
        
        public Builder city(String city) {
            this.city = city;
            return this;
        }
        
        public Builder state(String state) {
            this.state = state;
            return this;
        }
        
        public Builder postalCode(String postalCode) {
            this.postalCode = postalCode;
            return this;
        }
        
        public Builder country(String country) {
            this.country = country;
            return this;
        }
        
        public Builder isPrimary(boolean isPrimary) {
            this.isPrimary = isPrimary;
            return this;
        }
        
        public Builder isActive(boolean isActive) {
            this.isActive = isActive;
            return this;
        }
        
        public CustomerAddress build() {
            Objects.requireNonNull(streetAddress, "Street address is required");
            Objects.requireNonNull(city, "City is required");
            Objects.requireNonNull(postalCode, "Postal code is required");
            return new CustomerAddress(this);
        }
    }
    
    // Getters and setters
    public UUID getId() { return id; }
    public Customer getCustomer() { return customer; }
    public void setCustomer(Customer customer) { this.customer = customer; }
    public AddressType getAddressType() { return addressType; }
    public String getStreetAddress() { return streetAddress; }
    public String getAddressLine2() { return addressLine2; }
    public String getCity() { return city; }
    public String getState() { return state; }
    public String getPostalCode() { return postalCode; }
    public String getCountry() { return country; }
    public boolean isPrimary() { return isPrimary; }
    public boolean isActive() { return isActive; }
    public LocalDateTime getCreatedAt() { return createdAt; }
    public LocalDateTime getUpdatedAt() { return updatedAt; }
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CustomerAddress that = (CustomerAddress) o;
        return Objects.equals(id, that.id);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(id);
    }
    
    @Override
    public String toString() {
        return "CustomerAddress{" +
                "id=" + id +
                ", addressType=" + addressType +
                ", city='" + city + '\'' +
                ", country='" + country + '\'' +
                ", isPrimary=" + isPrimary +
                '}';
    }
}
