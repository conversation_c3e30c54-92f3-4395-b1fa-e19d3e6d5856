package com.nttdata.ndvn.customer.domain.model;

import jakarta.persistence.*;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.UUID;

/**
 * Customer note entity for storing notes and comments about customers.
 */
@Entity
@Table(name = "customer_notes", indexes = {
    @Index(name = "idx_customer_note_customer_id", columnList = "customer_id"),
    @Index(name = "idx_customer_note_type", columnList = "noteType"),
    @Index(name = "idx_customer_note_created_at", columnList = "createdAt")
})
public class CustomerNote {
    
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "customer_id", nullable = false)
    private Customer customer;
    
    @Column(name = "note_type", length = 50)
    private String noteType = "GENERAL";
    
    @Column(name = "subject")
    private String subject;
    
    @Column(name = "content", nullable = false, columnDefinition = "TEXT")
    private String content;
    
    @Column(name = "is_internal")
    private boolean isInternal = true;
    
    @Column(name = "created_by", nullable = false, length = 100)
    private String createdBy;
    
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    // Constructors
    protected CustomerNote() {
        // JPA constructor
    }
    
    private CustomerNote(Builder builder) {
        this.noteType = builder.noteType;
        this.subject = builder.subject;
        this.content = builder.content;
        this.isInternal = builder.isInternal;
        this.createdBy = builder.createdBy;
        this.createdAt = LocalDateTime.now();
    }
    
    public static Builder builder() {
        return new Builder();
    }
    
    // Builder pattern
    public static class Builder {
        private String noteType = "GENERAL";
        private String subject;
        private String content;
        private boolean isInternal = true;
        private String createdBy;
        
        public Builder noteType(String noteType) {
            this.noteType = noteType;
            return this;
        }
        
        public Builder subject(String subject) {
            this.subject = subject;
            return this;
        }
        
        public Builder content(String content) {
            this.content = content;
            return this;
        }
        
        public Builder isInternal(boolean isInternal) {
            this.isInternal = isInternal;
            return this;
        }
        
        public Builder createdBy(String createdBy) {
            this.createdBy = createdBy;
            return this;
        }
        
        public CustomerNote build() {
            Objects.requireNonNull(content, "Content is required");
            Objects.requireNonNull(createdBy, "Created by is required");
            return new CustomerNote(this);
        }
    }
    
    // Getters and setters
    public UUID getId() { return id; }
    public Customer getCustomer() { return customer; }
    public void setCustomer(Customer customer) { this.customer = customer; }
    public String getNoteType() { return noteType; }
    public String getSubject() { return subject; }
    public String getContent() { return content; }
    public boolean isInternal() { return isInternal; }
    public String getCreatedBy() { return createdBy; }
    public LocalDateTime getCreatedAt() { return createdAt; }
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CustomerNote that = (CustomerNote) o;
        return Objects.equals(id, that.id);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(id);
    }
    
    @Override
    public String toString() {
        return "CustomerNote{" +
                "id=" + id +
                ", noteType='" + noteType + '\'' +
                ", subject='" + subject + '\'' +
                ", isInternal=" + isInternal +
                ", createdBy='" + createdBy + '\'' +
                '}';
    }
}
