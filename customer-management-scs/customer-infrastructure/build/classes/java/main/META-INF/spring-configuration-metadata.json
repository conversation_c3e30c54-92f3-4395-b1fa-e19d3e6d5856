{"groups": [{"name": "spring.datasource.hikari", "type": "com.zaxxer.hikari.HikariConfig", "sourceType": "com.nttdata.ndvn.customer.infrastructure.config.DatabaseConfig", "sourceMethod": "hikariConfig()"}, {"name": "spring.datasource.hikari.health-check-registry", "type": "java.lang.Object", "sourceType": "com.zaxxer.hikari.HikariConfig", "sourceMethod": "getHealthCheckRegistry()"}, {"name": "spring.datasource.hikari.metric-registry", "type": "java.lang.Object", "sourceType": "com.zaxxer.hikari.HikariConfig", "sourceMethod": "getMetricRegistry()"}], "properties": [{"name": "spring.datasource.hikari.allow-pool-suspension", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.zaxxer.hikari.HikariConfig"}, {"name": "spring.datasource.hikari.auto-commit", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.zaxxer.hikari.HikariConfig"}, {"name": "spring.datasource.hikari.catalog", "type": "java.lang.String", "sourceType": "com.zaxxer.hikari.HikariConfig"}, {"name": "spring.datasource.hikari.connection-init-sql", "type": "java.lang.String", "sourceType": "com.zaxxer.hikari.HikariConfig"}, {"name": "spring.datasource.hikari.connection-test-query", "type": "java.lang.String", "sourceType": "com.zaxxer.hikari.HikariConfig"}, {"name": "spring.datasource.hikari.connection-timeout", "type": "java.lang.Long", "sourceType": "com.zaxxer.hikari.HikariConfig"}, {"name": "spring.datasource.hikari.data-source-class-name", "type": "java.lang.String", "sourceType": "com.zaxxer.hikari.HikariConfig"}, {"name": "spring.datasource.hikari.data-source-j-n-d-i", "type": "java.lang.String", "sourceType": "com.zaxxer.hikari.HikariConfig"}, {"name": "spring.datasource.hikari.data-source-properties", "type": "java.util.Properties", "sourceType": "com.zaxxer.hikari.HikariConfig"}, {"name": "spring.datasource.hikari.driver-class-name", "type": "java.lang.String", "sourceType": "com.zaxxer.hikari.HikariConfig"}, {"name": "spring.datasource.hikari.exception-override-class-name", "type": "java.lang.String", "sourceType": "com.zaxxer.hikari.HikariConfig"}, {"name": "spring.datasource.hikari.health-check-properties", "type": "java.util.Properties", "sourceType": "com.zaxxer.hikari.HikariConfig"}, {"name": "spring.datasource.hikari.idle-timeout", "type": "java.lang.Long", "sourceType": "com.zaxxer.hikari.HikariConfig"}, {"name": "spring.datasource.hikari.initialization-fail-timeout", "type": "java.lang.Long", "sourceType": "com.zaxxer.hikari.HikariConfig"}, {"name": "spring.datasource.hikari.isolate-internal-queries", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.zaxxer.hikari.HikariConfig"}, {"name": "spring.datasource.hikari.jdbc-url", "type": "java.lang.String", "sourceType": "com.zaxxer.hikari.HikariConfig"}, {"name": "spring.datasource.hikari.keepalive-time", "type": "java.lang.Long", "sourceType": "com.zaxxer.hikari.HikariConfig"}, {"name": "spring.datasource.hikari.leak-detection-threshold", "type": "java.lang.Long", "sourceType": "com.zaxxer.hikari.HikariConfig"}, {"name": "spring.datasource.hikari.max-lifetime", "type": "java.lang.Long", "sourceType": "com.zaxxer.hikari.HikariConfig"}, {"name": "spring.datasource.hikari.maximum-pool-size", "type": "java.lang.Integer", "sourceType": "com.zaxxer.hikari.HikariConfig"}, {"name": "spring.datasource.hikari.metrics-tracker-factory", "type": "com.zaxxer.hikari.metrics.MetricsTrackerFactory", "sourceType": "com.zaxxer.hikari.HikariConfig"}, {"name": "spring.datasource.hikari.minimum-idle", "type": "java.lang.Integer", "sourceType": "com.zaxxer.hikari.HikariConfig"}, {"name": "spring.datasource.hikari.password", "type": "java.lang.String", "sourceType": "com.zaxxer.hikari.HikariConfig"}, {"name": "spring.datasource.hikari.pool-name", "type": "java.lang.String", "sourceType": "com.zaxxer.hikari.HikariConfig"}, {"name": "spring.datasource.hikari.read-only", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.zaxxer.hikari.HikariConfig"}, {"name": "spring.datasource.hikari.register-mbeans", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.zaxxer.hikari.HikariConfig"}, {"name": "spring.datasource.hikari.scheduled-executor", "type": "java.util.concurrent.ScheduledExecutorService", "sourceType": "com.zaxxer.hikari.HikariConfig"}, {"name": "spring.datasource.hikari.schema", "type": "java.lang.String", "sourceType": "com.zaxxer.hikari.HikariConfig"}, {"name": "spring.datasource.hikari.transaction-isolation", "type": "java.lang.String", "sourceType": "com.zaxxer.hikari.HikariConfig"}, {"name": "spring.datasource.hikari.username", "type": "java.lang.String", "sourceType": "com.zaxxer.hikari.HikariConfig"}, {"name": "spring.datasource.hikari.validation-timeout", "type": "java.lang.Long", "sourceType": "com.zaxxer.hikari.HikariConfig"}], "hints": []}