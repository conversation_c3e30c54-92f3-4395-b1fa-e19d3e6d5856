plugins {
    id 'java'
    id 'org.springframework.boot' version '3.4.1'
    id 'io.spring.dependency-management' version '1.1.7'
    id 'org.sonarqube' version '5.1.0.4882'
    id 'jacoco'
}

allprojects {
    group = 'com.nttdata.ndvn'
    version = '1.0.0-SNAPSHOT'
    
    repositories {
        mavenCentral()
        maven { url 'https://packages.confluent.io/maven/' }
    }
}

subprojects {
    apply plugin: 'java'
    apply plugin: 'org.springframework.boot'
    apply plugin: 'io.spring.dependency-management'
    apply plugin: 'jacoco'
    
    java {
        toolchain {
            languageVersion = JavaLanguageVersion.of(21)
        }
    }
    
    configurations {
        compileOnly {
            extendsFrom annotationProcessor
        }
    }
    
    dependencyManagement {
        imports {
            mavenBom 'org.springframework.cloud:spring-cloud-dependencies:2024.0.0'
            mavenBom 'org.testcontainers:testcontainers-bom:1.20.4'
        }
    }
    
    dependencies {
        // Common dependencies for all modules
        implementation 'org.springframework.boot:spring-boot-starter'
        implementation 'org.springframework.boot:spring-boot-starter-logging'
        implementation 'org.springframework.boot:spring-boot-starter-validation'
        
        // Configuration
        annotationProcessor 'org.springframework.boot:spring-boot-configuration-processor'
        
        // Testing
        testImplementation 'org.springframework.boot:spring-boot-starter-test'
        testImplementation 'org.junit.jupiter:junit-jupiter'
        testRuntimeOnly 'org.junit.platform:junit-platform-launcher'
    }
    
    test {
        useJUnitPlatform()
        finalizedBy jacocoTestReport
    }
    
    jacocoTestReport {
        dependsOn test
        reports {
            xml.required = true
            html.required = true
        }
    }
}

// Module-specific configurations
configure(project(':customer-domain')) {
    dependencies {
        // Domain layer - minimal dependencies
        implementation 'org.springframework.data:spring-data-commons'
        implementation 'jakarta.persistence:jakarta.persistence-api'
        implementation 'org.springframework.security:spring-security-core'
        
        // Testing
        testImplementation 'org.mockito:mockito-core'
        testImplementation 'org.mockito:mockito-junit-jupiter'
    }
}

configure(project(':customer-infrastructure')) {
    dependencies {
        implementation project(':customer-domain')
        
        // Database
        implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
        implementation 'org.postgresql:postgresql'
        implementation 'org.flywaydb:flyway-core'
        implementation 'org.flywaydb:flyway-database-postgresql'
        
        // Caching
        implementation 'org.springframework.boot:spring-boot-starter-data-redis'
        implementation 'org.springframework.boot:spring-boot-starter-cache'
        
        // Connection pooling
        implementation 'com.zaxxer:HikariCP'
        
        // Testing
        testImplementation 'org.testcontainers:postgresql'
        testImplementation 'org.testcontainers:junit-jupiter'
        testImplementation 'com.h2database:h2'
    }
}

configure(project(':customer-application')) {
    dependencies {
        implementation project(':customer-domain')
        implementation project(':customer-infrastructure')

        // Spring Data for Pageable and Page
        implementation 'org.springframework.data:spring-data-commons'
        implementation 'org.springframework:spring-tx'

        // MapStruct for DTO mapping
        implementation 'org.mapstruct:mapstruct:1.6.3'
        annotationProcessor 'org.mapstruct:mapstruct-processor:1.6.3'

        // JSON processing
        implementation 'com.fasterxml.jackson.core:jackson-databind'
        implementation 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310'

        // Testing
        testImplementation 'org.springframework.boot:spring-boot-starter-test'
    }
}

configure(project(':customer-events')) {
    dependencies {
        implementation project(':customer-domain')
        
        // Spring Security for UserDetails
        implementation 'org.springframework.security:spring-security-core'
        
        // Kafka
        implementation 'org.springframework.kafka:spring-kafka'
        implementation 'org.apache.kafka:kafka-clients'
        
        // Avro serialization
        implementation 'io.confluent:kafka-avro-serializer:7.5.0'
        implementation 'org.apache.avro:avro:1.11.3'
        
        // Testing
        testImplementation 'org.springframework.kafka:spring-kafka-test'
        testImplementation 'org.testcontainers:kafka'
    }
}

configure(project(':customer-web')) {
    dependencies {
        implementation project(':customer-domain')
        implementation project(':customer-infrastructure')
        implementation project(':customer-application')
        implementation project(':customer-events')
        
        // Web
        implementation 'org.springframework.boot:spring-boot-starter-web'
        implementation 'org.springframework.boot:spring-boot-starter-security'
        implementation 'org.springframework.boot:spring-boot-starter-oauth2-resource-server'
        implementation 'org.springframework.boot:spring-boot-starter-validation'
        implementation 'org.springframework.data:spring-data-commons'
        
        // Service Discovery
        implementation 'org.springframework.cloud:spring-cloud-starter-consul-discovery'
        implementation 'org.springframework.cloud:spring-cloud-starter-consul-config'
        implementation 'org.springframework.cloud:spring-cloud-starter-loadbalancer'
        
        // Monitoring
        implementation 'org.springframework.boot:spring-boot-starter-actuator'
        implementation 'io.micrometer:micrometer-registry-prometheus'
        implementation 'io.micrometer:micrometer-tracing-bridge-brave'
        implementation 'io.zipkin.reporter2:zipkin-reporter-brave'
        
        // API Documentation
        implementation 'org.springdoc:springdoc-openapi-starter-webmvc-ui:2.3.0'
        
        // Testing
        testImplementation 'org.springframework.boot:spring-boot-starter-test'
        testImplementation 'org.springframework.security:spring-security-test'
        testImplementation 'org.springframework:spring-tx'
        testImplementation 'org.testcontainers:junit-jupiter'
        testImplementation 'org.testcontainers:postgresql'
        testImplementation 'org.testcontainers:kafka'
    }
}

// Root project configuration
jar {
    enabled = false
}

bootJar {
    enabled = false
}

// SonarQube configuration
sonar {
    properties {
        property 'sonar.projectName', 'Customer Management SCS'
        property 'sonar.projectKey', 'ndvn-customer-management-scs'
        property 'sonar.coverage.jacoco.xmlReportPaths', '**/build/reports/jacoco/test/jacocoTestReport.xml'
        property 'sonar.junit.reportPaths', '**/build/test-results/test'
    }
}
