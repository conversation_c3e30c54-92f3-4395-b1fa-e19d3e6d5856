package com.nttdata.ndvn.auth;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * Token Service Application for NDVN SCS Platform
 * 
 * This service provides token introspection, validation, and caching
 * for the SCS platform authentication and authorization.
 */
@SpringBootApplication
public class TokenServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(TokenServiceApplication.class, args);
    }
}
